<?php
/**
 * Nodeloc登录SDK
 * 1.0
 * https://nodeloc.cc/u/pastking/summary
**/
// ClientId 在 https://conn.nodeloc.cc/apps 获取
$ClientId = '';

// ClientSecret 在 https://conn.nodeloc.cc/apps 获取
$ClientSecret = '';

// 回调地址配置 - 必须与在 Nodeloc 应用管理中注册的地址完全一致
// 方式1: 手动设置完整的回调地址（推荐生产环境使用）
$RedirectUri = '';  // 例如: 'https://yourdomain.com/oauth2/return.php'

// 方式2: 如果$RedirectUri为空，则自动构建回调地址
if (empty($RedirectUri)) {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $RedirectUri = $protocol . $host . $script_dir . 'return.php';
}