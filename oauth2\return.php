<?php
/**
 * Nodeloc登录SDK
 * 1.0
 * https://nodeloc.cc/u/pastking/summary
**/
session_start(); // 启动 session
@header('Content-Type: text/html; charset=UTF-8');
include('config.php');

$code = $_GET['code'] ?? '';
$state = $_GET['state'] ?? '';
$error = $_GET['error'] ?? '';

// 检查是否有错误
if ($error) {
    ?>
    <!DOCTYPE html>
    <html lang="zh-cn">
    <head>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>授权失败 - Nodeloc OAuth2</title>
        <style>
            body {
                background-color: #f5f7fa;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .error-container {
                text-align: center;
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border: 1px solid #e1e8ed;
                max-width: 500px;
            }
            
            .error-icon {
                font-size: 48px;
                color: #dc3545;
                margin-bottom: 20px;
            }
            
            .error-title {
                font-size: 24px;
                font-weight: 600;
                color: #333;
                margin-bottom: 15px;
            }
            
            .error-message {
                font-size: 14px;
                color: #666;
                margin-bottom: 25px;
                padding: 15px;
                background: #f8d7da;
                border-radius: 4px;
                border: 1px solid #f5c6cb;
                line-height: 1.5;
                color: #721c24;
            }
            
            .back-link {
                display: inline-block;
                padding: 10px 20px;
                background: #4a90e2;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
            }
            
            .back-link:hover {
                background: #357abd;
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">❌</div>
            
            <div class="error-title">授权失败</div>
            
            <div class="error-message">
                <strong>错误详情：</strong><br>
                <?php echo htmlspecialchars($error); ?>
            </div>
            
            <a href="./" class="back-link">返回首页重试</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 验证state参数，防止CSRF攻击
if (empty($state) || $state !== ($_SESSION['oauth_state'] ?? '')) {
    ?>
    <!DOCTYPE html>
    <html lang="zh-cn">
    <head>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>安全验证失败 - Nodeloc OAuth2</title>
        <style>
            body {
                background-color: #f5f7fa;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .error-container {
                text-align: center;
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border: 1px solid #e1e8ed;
                max-width: 500px;
            }
            
            .error-icon {
                font-size: 48px;
                color: #ffc107;
                margin-bottom: 20px;
            }
            
            .error-title {
                font-size: 24px;
                font-weight: 600;
                color: #333;
                margin-bottom: 15px;
            }
            
            .error-message {
                font-size: 14px;
                color: #666;
                margin-bottom: 25px;
                padding: 15px;
                background: #fff3cd;
                border-radius: 4px;
                border: 1px solid #ffeaa7;
                line-height: 1.6;
                color: #856404;
            }
            
            .back-link {
                display: inline-block;
                padding: 10px 20px;
                background: #4a90e2;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
            }
            
            .back-link:hover {
                background: #357abd;
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">⚠️</div>
            
            <div class="error-title">安全验证失败</div>
            
            <div class="error-message">
                <strong>State 参数验证失败</strong><br><br>
                这可能是由于以下原因造成的：<br>
                • 可能存在 CSRF 攻击尝试<br>
                • 会话过期或被清除<br>
                • 重复使用了授权链接<br><br>
                为了您的安全，请重新开始登录流程。
            </div>
            
            <a href="./" class="back-link">重新开始登录</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 清除已使用的state
unset($_SESSION['oauth_state']);

if (empty($code)) {
    ?>
    <!DOCTYPE html>
    <html lang="zh-cn">
    <head>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>授权码缺失 - Nodeloc OAuth2</title>
        <style>
            body {
                background-color: #f5f7fa;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .error-container {
                text-align: center;
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border: 1px solid #e1e8ed;
                max-width: 500px;
            }
            
            .error-icon {
                font-size: 48px;
                color: #dc3545;
                margin-bottom: 20px;
            }
            
            .error-title {
                font-size: 24px;
                font-weight: 600;
                color: #333;
                margin-bottom: 15px;
            }
            
            .error-message {
                font-size: 14px;
                color: #666;
                margin-bottom: 25px;
                padding: 15px;
                background: #f8d7da;
                border-radius: 4px;
                border: 1px solid #f5c6cb;
                line-height: 1.6;
                color: #721c24;
            }
            
            .back-link {
                display: inline-block;
                padding: 10px 20px;
                background: #4a90e2;
                color: white;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
            }
            
            .back-link:hover {
                background: #357abd;
                color: white;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">❌</div>
            
            <div class="error-title">授权码缺失</div>
            
            <div class="error-message">
                <strong>未获取到有效的授权码</strong><br><br>
                这可能是由于以下原因造成的：<br>
                • 授权流程被中断<br>
                • 用户拒绝了授权请求<br>
                • 回调地址配置错误<br><br>
                请重新开始登录流程。
            </div>
            
            <a href="./" class="back-link">返回首页重试</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 构建Basic认证头
$auth_header = 'Basic ' . base64_encode($ClientId . ':' . $ClientSecret);

$headers = [
    'Authorization: ' . $auth_header,
    'Content-Type: application/x-www-form-urlencoded'
];

// 准备token交换请求数据
$post_data = http_build_query([
    'grant_type' => 'authorization_code',
    'code' => $code,
    'redirect_uri' => $RedirectUri  // 使用配置的回调地址，必须与授权时一致
]);

// 获取访问令牌
$getTokenRes = get_curl('https://conn.nodeloc.cc/oauth2/token', $post_data, 0, 0, $headers);

$getTokenArr = json_decode($getTokenRes, true);

if (isset($getTokenArr['access_token'])) {
    $access_token = $getTokenArr['access_token'];
    $id_token = $getTokenArr['id_token'] ?? '';
    $refresh_token = $getTokenArr['refresh_token'] ?? '';
    
    // 使用access token获取用户信息
    $user_headers = [
        'Authorization: Bearer ' . $access_token
    ];

    $getUserRes = get_curl('https://conn.nodeloc.cc/oauth2/userinfo', 0, 0, 0, $user_headers);
    
    $getUserArr = json_decode($getUserRes, true);
    
    if (isset($getUserArr['sub'])) {
        // 保存用户数据到 session 中
        $_SESSION['user_id'] = $getUserArr['sub'];  // 使用sub作为用户ID
        $_SESSION['user_username'] = $getUserArr['preferred_username'] ?? '';  // 用户名
        $_SESSION['user_name'] = $getUserArr['name'] ?? '';  // 显示名称
        $_SESSION['user_email'] = $getUserArr['email'] ?? '';
        $_SESSION['user_picture'] = $getUserArr['picture'] ?? '';  // 头像URL
        $_SESSION['user_email_verified'] = $getUserArr['email_verified'] ?? false;  // 邮箱验证状态
        $_SESSION['user_groups'] = $getUserArr['groups'] ?? [];
        
        // 保存令牌信息
        $_SESSION['access_token'] = $access_token;
        $_SESSION['refresh_token'] = $refresh_token;
        $_SESSION['id_token'] = $id_token;
        
        // 如果有ID Token，解析其中的用户信息
        if ($id_token) {
            $id_token_data = parseJwtPayload($id_token);
            if ($id_token_data) {
                $_SESSION['id_token_data'] = $id_token_data;
                
                // 如果userinfo端点没有返回完整信息，从ID Token中补充
                if (empty($_SESSION['user_username']) && !empty($id_token_data['preferred_username'])) {
                    $_SESSION['user_username'] = $id_token_data['preferred_username'];
                }
                if (empty($_SESSION['user_name']) && !empty($id_token_data['name'])) {
                    $_SESSION['user_name'] = $id_token_data['name'];
                }
                if (empty($_SESSION['user_picture']) && !empty($id_token_data['picture'])) {
                    $_SESSION['user_picture'] = $id_token_data['picture'];
                }
            }
        }
        
        // 登录成功，重定向到首页
        exit("<script language='javascript'>window.location.href='./';</script>");
    } else {
        echo '<h3>用户信息获取失败</h3>';
        echo '<pre>' . htmlspecialchars(json_encode($getUserArr, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
    }
} else {
    echo '<h3>Token交换失败</h3>';
    echo '<pre>' . htmlspecialchars(json_encode($getTokenArr, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
}

// JWT Token解析函数
function parseJwtPayload($jwt) {
    $parts = explode('.', $jwt);
    if (count($parts) !== 3) {
        return null;
    }
    
    $payload = $parts[1];
    // 添加必要的填充
    $payload .= str_repeat('=', (4 - strlen($payload) % 4) % 4);
    
    $decoded = base64_decode(strtr($payload, '-_', '+/'));
    return json_decode($decoded, true);
}

// cURL 函数
function get_curl($url, $post=0, $referer=0, $cookie=0, $header=0, $ua=0, $nobaody=0, $addheader=0)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $httpheader[] = "Accept: application/json";
    $httpheader[] = "Accept-Encoding: gzip,deflate";
    $httpheader[] = "Accept-Language: zh-CN,zh;q=0.8";
    $httpheader[] = "Connection: close";
    
    if ($header) {
        $httpheader = array_merge($httpheader, $header);
    }
    curl_setopt($ch, CURLOPT_HTTPHEADER, $httpheader);
    
    if ($post) {
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post);
    }
    
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    
    if ($referer) {
        if ($referer == 1) {
            curl_setopt($ch, CURLOPT_REFERER, '');
        } else {
            curl_setopt($ch, CURLOPT_REFERER, $referer);
        }
    }
    
    if ($ua) {
        curl_setopt($ch, CURLOPT_USERAGENT, $ua);
    } else {
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
    }
    
    if ($nobaody) {
        curl_setopt($ch, CURLOPT_NOBODY, 1);
    }
    
    curl_setopt($ch, CURLOPT_ENCODING, "gzip");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $ret = curl_exec($ch);
    curl_close($ch);
    return $ret;
}
