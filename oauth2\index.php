<?php
error_reporting(0);
session_start();
@header('Content-Type: text/html; charset=UTF-8');

?><!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="initial-scale=1, maximum-scale=1, user-scalable=no, width=device-width">
  <meta name="renderer" content="webkit"/>
  <title>Nodeloc OAuth2 登录SDK</title>
  <link href="//lib.baomitu.com/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet"/>
  <style>
  body {
    background-color: #f5f7fa;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    padding: 20px 0;
  }
  
  .main-container {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .panel {
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    background: white;
  }
  
  .panel-heading {
    background: #4a90e2;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    border: none;
  }
  
  .panel-heading.panel-success {
    background: #5cb85c;
  }
  
  .panel-heading.panel-info {
    background: #5bc0de;
  }
  
  .panel-heading.panel-warning {
    background: #f0ad4e;
  }
  
  .panel-heading.panel-default {
    background: #777;
  }
  
  .panel-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }
  
  .panel-body {
    padding: 20px;
  }
  
  .form-control {
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 10px 12px;
    font-size: 14px;
  }
  
  .form-control:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
  
  .btn {
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 500;
    border: none;
  }
  
  .btn-primary {
    background: #4a90e2;
    color: white;
  }
  
  .btn-primary:hover {
    background: #357abd;
    color: white;
  }
  
  .btn-warning {
    background: #f0ad4e;
    color: white;
  }
  
  .btn-warning:hover {
    background: #ec971f;
    color: white;
  }
  
  .avatar-container {
    text-align: center;
    margin-bottom: 20px;
  }
  
  .avatar-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #5cb85c;
    object-fit: cover;
  }
  
  .form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
  }
  
  .verification-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
  }
  
  .verified {
    background: #d4edda;
    color: #155724;
  }
  
  .unverified {
    background: #f8d7da;
    color: #721c24;
  }
  
  .code-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    word-break: break-all;
  }
  
  .help-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
  }
  
  .section-divider {
    border-bottom: 1px solid #e1e8ed;
    margin: 20px 0;
    padding-bottom: 10px;
  }
  
  @media (max-width: 768px) {
    body {
      padding: 10px;
    }
    
    .panel-body {
      padding: 15px;
    }
    
    .avatar-img {
      width: 60px;
      height: 60px;
    }
  }
  </style>
</head>
<body>
<div class="container-fluid main-container">
    
    <!-- 主登录面板 -->
    <div class="panel">
        <div class="panel-heading">
            <h3 class="panel-title text-center">Nodeloc OAuth2 登录SDK</h3>
        </div>
        <div class="panel-body text-center">
            <form action="./connect.php" method="get" role="form">
                <button type="submit" class="btn btn-primary btn-block btn-lg">开始登录</button>
            </form>
            
            <?php if(isset($_SESSION['user_id'])){ ?>
            <div style="margin-top: 15px;">
                <a href="logout.php" class="btn btn-warning btn-block">退出登录</a>
            </div>
            <?php } ?>
        </div>
    </div>

    <?php if(isset($_SESSION['user_id'])){?>
    <!-- 用户信息面板 -->
    <div class="panel">
        <div class="panel-heading panel-success">
            <h3 class="panel-title text-center">登录成功</h3>
        </div>
        <div class="panel-body">
            <?php if(!empty($_SESSION['user_picture'])){ ?>
            <div class="avatar-container">
                <img src="<?php echo htmlspecialchars($_SESSION['user_picture']); ?>" 
                     alt="用户头像" 
                     class="avatar-img">
            </div>
            <?php } ?>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>用户ID</label>
                        <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_id'] ?? ''); ?>" 
                               class="form-control" readonly/>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_username'] ?? ''); ?>" 
                               class="form-control" readonly/>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>显示名称</label>
                        <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_name'] ?? ''); ?>" 
                               class="form-control" readonly/>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>邮箱
                            <?php if(isset($_SESSION['user_email_verified']) && $_SESSION['user_email_verified']): ?>
                                <span class="verification-status verified">已验证</span>
                            <?php else: ?>
                                <span class="verification-status unverified">未验证</span>
                            <?php endif; ?>
                        </label>
                        <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_email'] ?? ''); ?>" 
                               class="form-control" readonly/>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label>头像URL</label>
                <input type="text" value="<?php echo htmlspecialchars($_SESSION['user_picture'] ?? ''); ?>" 
                       class="form-control" readonly/>
            </div>
            
            <div class="form-group">
                <label>用户组</label>
                <textarea class="form-control" rows="2" readonly><?php echo htmlspecialchars(json_encode($_SESSION['user_groups'] ?? [], JSON_UNESCAPED_UNICODE)); ?></textarea>
            </div>
        </div>
    </div>

    <!-- 令牌信息面板 -->
    <div class="panel">
        <div class="panel-heading panel-info">
            <h3 class="panel-title text-center">令牌信息</h3>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <label>访问令牌 (Access Token)</label>
                <textarea class="form-control" rows="3" readonly><?php echo htmlspecialchars($_SESSION['access_token'] ?? ''); ?></textarea>
            </div>
            
            <?php if(!empty($_SESSION['refresh_token'])){ ?>
            <div class="form-group">
                <label>刷新令牌 (Refresh Token)</label>
                <textarea class="form-control" rows="3" readonly><?php echo htmlspecialchars($_SESSION['refresh_token']); ?></textarea>
            </div>
            <?php } ?>
            
            <?php if(!empty($_SESSION['id_token'])){ ?>
            <div class="form-group">
                <label>ID令牌 (ID Token)</label>
                <textarea class="form-control" rows="3" readonly><?php echo htmlspecialchars($_SESSION['id_token']); ?></textarea>
            </div>
            <?php } ?>
        </div>
    </div>

    <?php if(!empty($_SESSION['id_token_data'])){ ?>
    <!-- ID Token 解析数据面板 -->
    <div class="panel">
        <div class="panel-heading panel-warning">
            <h3 class="panel-title text-center">ID Token 解析数据</h3>
        </div>
        <div class="panel-body">
            <div class="code-display"><?php echo htmlspecialchars(json_encode($_SESSION['id_token_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></div>
        </div>
    </div>
    <?php } ?>

    <?php }?>

</div>
</body>
</html>