<?php
/**
 * Nodeloc登录SDK
 * 1.0
 * https://nodeloc.cc/
**/

error_reporting(0);
session_start();
@header('Content-Type: text/html; charset=UTF-8');
include('config.php');

if($ClientId==''){
    die('请先在config.php中填入密钥');
}

if($RedirectUri==''){
    die('请先在config.php中填入回调地址');
}

// 生成随机state参数，用于防CSRF攻击
$state = bin2hex(random_bytes(16));
$_SESSION['oauth_state'] = $state;

// 构建授权URL - 根据ABOUT.md中的规范
$params = [
    'response_type' => 'code',
    'client_id' => $ClientId,
    'redirect_uri' => $RedirectUri,  // 使用配置的回调地址
    'scope' => 'openid profile',
    'state' => $state
];

$Oauth_url = 'https://conn.nodeloc.cc/oauth2/auth?' . http_build_query($params);

?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到 Nodeloc 授权页面...</title>
    <style>
        body {
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .loading-container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e8ed;
            max-width: 400px;
        }
        
        .loading-icon {
            font-size: 48px;
            color: #4a90e2;
            margin-bottom: 20px;
        }
        
        .loading-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .loading-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: #4a90e2;
            animation: progress 3s ease-out;
        }
        
        .manual-link {
            display: inline-block;
            padding: 10px 20px;
            background: #4a90e2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .manual-link:hover {
            background: #357abd;
            color: white;
            text-decoration: none;
        }
        
        .security-info {
            font-size: 13px;
            color: #666;
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="loading-icon">⏳</div>
        
        <div class="loading-title">正在跳转到 Nodeloc</div>
        
        <div class="loading-text">
            正在为您跳转到 Nodeloc 平台进行安全授权...
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="security-info">
            🛡️ 您将被重定向到 Nodeloc 官方授权页面<br>
            请在授权页面确认您的登录信息
        </div>
        
        <br>
        <a href="<?php echo htmlspecialchars($Oauth_url); ?>" class="manual-link">手动跳转</a>
    </div>

    <script>
        // 3秒后自动跳转
        setTimeout(function() {
            window.location.href = '<?php echo addslashes($Oauth_url); ?>';
        }, 3000);
    </script>
</body>
</html>
