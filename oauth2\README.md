# Nodeloc OAuth2 登录 SDK

这是一个用于集成 Nodeloc 平台 OAuth2 登录的 PHP SDK，基于 OpenID Connect 1.0 协议实现。

## 功能特性

- 🔐 支持 Nodeloc 平台的 OAuth2 + OpenID Connect 授权登录
- 👤 获取用户基本信息（用户名、邮箱、用户组等）
- 🔑 支持访问令牌和刷新令牌管理
- 🎯 JWT ID Token 解析功能
- 🛡️ 内置 CSRF 防护（state 参数验证）
- 💾 完整的会话管理
- 🚀 简单易用的 PHP 实现

## 技术规范

- **协议**: OAuth 2.0 + OpenID Connect 1.0
- **基础URL**: `https://conn.nodeloc.cc`
- **认证方式**: Basic Auth (Client Credentials)
- **支持的授权类型**: Authorization Code

## 安装配置

### 1. 申请 OAuth2 应用

访问 [https://conn.nodeloc.cc/apps](https://conn.nodeloc.cc/apps) 创建 OAuth2 应用，获取：
- Client ID（客户端ID）
- Client Secret（客户端密钥）

### 2. 配置密钥和回调地址

编辑 `config.php` 文件，填入您的应用密钥：

```php
// ClientId 在 https://conn.nodeloc.cc/apps 获取
$ClientId = '您的客户端ID';

// ClientSecret 在 https://conn.nodeloc.cc/apps 获取
$ClientSecret = '您的客户端密钥';

// 回调地址配置 - 必须与在 Nodeloc 应用管理中注册的地址完全一致
// 方式1: 手动设置完整的回调地址（推荐生产环境使用）
$RedirectUri = 'https://yourdomain.com/oauth2/return.php';

// 方式2: 留空则自动检测（适合开发测试）
// $RedirectUri = '';
```

**重要提示**：
- 生产环境建议手动设置完整的回调地址
- 回调地址必须与在 Nodeloc 应用管理中注册的地址完全一致
- 支持 HTTP（开发）和 HTTPS（生产）协议

## 文件说明

- `config.php` - 配置文件，存储 OAuth2 应用密钥
- `index.php` - 主页面，显示登录按钮和用户信息
- `connect.php` - 发起 OAuth2 授权请求
- `return.php` - 处理 OAuth2 回调，获取用户信息和令牌
- `logout.php` - 退出登录，清除会话数据

## 使用方法

1. 将所有文件上传到您的 Web 服务器
2. 配置好 `config.php` 中的密钥
3. 在 Nodeloc 应用管理中设置正确的回调地址
4. 访问 `index.php` 开始测试登录

## OAuth2 流程

### 1. 授权流程
```
GET /oauth2/auth?response_type=code&client_id=CLIENT_ID&redirect_uri=CALLBACK_URL&scope=openid profile&state=RANDOM_STATE
```

### 2. 令牌交换
```
POST /oauth2/token
Authorization: Basic base64(client_id:client_secret)
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&code=AUTH_CODE&redirect_uri=CALLBACK_URL
```

### 3. 用户信息获取
```
GET /oauth2/userinfo
Authorization: Bearer ACCESS_TOKEN
```

## 获取的用户信息

成功登录后，可以通过 `$_SESSION` 获取以下用户信息：

### 用户基本信息
- `user_id` - 用户ID（sub字段）
- `user_username` - 用户名（preferred_username字段）
- `user_name` - 显示名称（name字段）
- `user_email` - 邮箱地址
- `user_picture` - 头像URL
- `user_email_verified` - 邮箱验证状态（布尔值）
- `user_groups` - 用户组数组

### 令牌信息
- `access_token` - 访问令牌
- `refresh_token` - 刷新令牌（如果提供）
- `id_token` - ID令牌（JWT格式）
- `id_token_data` - 解析后的ID令牌数据

## 安全特性

### 1. CSRF 防护
- 使用随机 `state` 参数防止 CSRF 攻击
- 授权回调时验证 state 参数

### 2. 令牌安全
- 使用 HTTPS 传输所有令牌
- 访问令牌存储在服务器端会话中
- 支持令牌过期处理

### 3. 输入验证
- 所有用户输入都经过 `htmlspecialchars()` 转义
- 检查必需参数的存在性

## 错误处理

SDK 会自动处理以下错误情况：

- **授权被拒绝**: 显示用户拒绝授权的信息
- **State验证失败**: 防止CSRF攻击
- **令牌交换失败**: 显示详细的错误信息
- **用户信息获取失败**: 显示API响应内容

## 高级功能

### JWT ID Token 解析

SDK 自动解析 ID Token 中的用户信息：

```php
// ID Token 包含的典型数据
{
  "sub": "123",           // 用户ID
  "username": "john_doe", // 用户名
  "email": "<EMAIL>", // 邮箱
  "groups": ["developers"], // 用户组
  "iat": 1640995200,     // 签发时间
  "exp": 1640998800      // 过期时间
}
```

### 令牌刷新

如果您的应用支持刷新令牌，可以使用类似以下的代码：

```php
// 刷新访问令牌的示例代码
$refresh_token = $_SESSION['refresh_token'];
// 调用 /oauth2/token 端点进行令牌刷新
```

## 系统要求

- PHP 7.0 或更高版本
- cURL 扩展
- OpenSSL 扩展（用于 HTTPS）
- 会话支持

## 注意事项

- 确保您的服务器支持 PHP 和 cURL
- **强烈建议**在 HTTPS 环境下使用
- 请妥善保管您的 Client Secret
- 定期检查和更新您的应用密钥
- 回调地址必须与注册时的地址完全匹配

## 故障排查

### 常见错误

1. **`invalid_client`**: 检查 Client ID 和 Client Secret
2. **`invalid_redirect_uri`**: 确保回调地址与注册时一致
3. **`access_denied`**: 用户拒绝授权或无权限
4. **State验证失败**: 可能的CSRF攻击或会话问题

## 技术支持

如有问题，请访问：
- [Nodeloc 社区](https://nodeloc.cc/)
- [OAuth2 应用管理](https://conn.nodeloc.cc/apps)

## 更新日志

### v1.0
- 初始版本发布
- 支持完整的 OAuth2 + OpenID Connect 流程
- 实现 JWT ID Token 解析
- 添加 CSRF 防护 