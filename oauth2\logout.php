<?php
/**
 * Nodeloc登录SDK - 退出登录
 * 1.0
 * https://nodeloc.cc/u/pastking/summary
**/

session_start();

// 检查是否已登录
$was_logged_in = isset($_SESSION['user_id']);

// 清除所有与用户相关的会话数据
unset($_SESSION['user_id']);
unset($_SESSION['user_username']);
unset($_SESSION['user_name']);
unset($_SESSION['user_email']);
unset($_SESSION['user_picture']);
unset($_SESSION['user_email_verified']);
unset($_SESSION['user_groups']);
unset($_SESSION['access_token']);
unset($_SESSION['refresh_token']);
unset($_SESSION['id_token']);
unset($_SESSION['id_token_data']);
unset($_SESSION['oauth_state']);

if (!$was_logged_in) {
    // 如果用户本来就没登录，直接重定向
    header("Location: ./");
    exit();
}

?>
<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录成功 - Nodeloc OAuth2</title>
    <style>
        body {
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .logout-container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e8ed;
            max-width: 450px;
        }
        
        .success-icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 20px;
        }
        
        .logout-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .logout-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        
        .countdown-container {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 25px;
        }
        
        .countdown-text {
            font-size: 14px;
            font-weight: 500;
            color: #155724;
            margin-bottom: 5px;
        }
        
        .countdown-number {
            font-size: 24px;
            font-weight: 700;
            color: #28a745;
            margin: 5px 0;
        }
        
        .back-link {
            display: inline-block;
            padding: 10px 20px;
            background: #4a90e2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: #357abd;
            color: white;
            text-decoration: none;
        }
        
        .security-info {
            font-size: 13px;
            color: #666;
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="success-icon">✅</div>
        
        <div class="logout-title">退出登录成功</div>
        
        <div class="logout-message">
            您已成功退出 Nodeloc OAuth2 登录<br>
            所有会话数据已安全清除
        </div>
        
        <div class="countdown-container">
            <div class="countdown-text">⏰ 页面将在以下秒数后自动跳转：</div>
            <div class="countdown-number" id="countdown">3</div>
        </div>
        
        <a href="./" class="back-link">立即返回首页</a>
        
        <div class="security-info">
            🛡️ <strong>安全提示：</strong><br>
            为了您的账户安全，建议在公共设备上使用后及时退出登录，
            并关闭浏览器窗口。
        </div>
    </div>

    <script>
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(function() {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = './';
            }
        }, 1000);
    </script>
</body>
</html> 