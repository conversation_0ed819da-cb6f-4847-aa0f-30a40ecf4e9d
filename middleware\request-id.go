package middleware

import (
	"context"
	"github.com/gin-gonic/gin"
	"one-api/common"
)

func RequestId() func(c *gin.Context) {
	return func(c *gin.Context) {
		id := common.GetTimeString() + common.GetRandomString(8)
		c.Set(common.RequestIdKey, id)
		ctx := context.WithValue(c.Request.Context(), common.RequestIdKey, id)
		c.Request = c.Request.WithContext(ctx)
		c.<PERSON><PERSON>(common.RequestIdKey, id)
		c.Next()
	}
}
