name: macOS Release
permissions:
  contents: write

on:
  workflow_dispatch:
    inputs:
      name:
        description: 'reason'
        required: false
  push:
    tags:
      - '*'
      - '!*-alpha*'
jobs:
  release:
    runs-on: macos-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest
      - name: Build Frontend
        env:
          CI: ""
          NODE_OPTIONS: "--max-old-space-size=4096"
        run: |
          cd web
          bun install
          DISABLE_ESLINT_PLUGIN='true' VITE_REACT_APP_VERSION=$(git describe --tags) bun run build
          cd ..
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: '>=1.18.0'
      - name: Build Backend
        run: |
          go mod download
          go build -ldflags "-X 'one-api/common.Version=$(git describe --tags)'" -o one-api-macos
      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: one-api-macos
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
